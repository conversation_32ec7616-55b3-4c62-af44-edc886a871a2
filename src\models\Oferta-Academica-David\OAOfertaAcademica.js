import { Model, DataTypes } from "sequelize";
import sequelize from "../../database/database.js";
import OAEtapa from "./OAEtapa.js";

class OAOfertaAcademica extends Model { }
OAOfertaAcademica.schema = "oferta_academica";
OAOfertaAcademica.init(
    {
        oa_id: {
            type: DataTypes.UUID,
            defaultValue: DataTypes.UUIDV4,
            primaryKey: true,
            allowNull: false,
        },
        etapa_id: {
            type: DataTypes.INTEGER,
            allowNull: false,
            references: { 
                model: OAEtapa,
                key: "id",
            },
            onUpdate: "CASCADE",
            onDelete: "RESTRICT",
        },
        cod_sede: {
            type: DataTypes.INTEGER,
            allowNull: false,
            validate: {
                isNumeric: true,
            },
        },
        nombre_sede: {
            type: DataTypes.STRING,
            allowNull: true,
            // validate: {
            //     is: /^[A-ZÀ-ÖØ-Þ0-9\s]+$/,
            // },
        },
        cod_carrera: {
            type: DataTypes.INTEGER,
            allowNull: true,
            validate: {
                isNumeric: true,
            },
        },
        nombre_carrera: {
            type: DataTypes.STRING,
            allowNull: true,
            // validate: {
            //     is: /^[A-ZÀ-ÖØ-Þ0-9\s]+$/,
            // },
        },
        modalidad: {
            type: DataTypes.INTEGER,
            allowNull: true,
            validate: {
                isIn: [[1, 2, 3]],
            },
        },
        cod_jornada: {
            type: DataTypes.INTEGER,
            allowNull: true,
            validate: {
                isIn: [[1, 2, 3, 4, 5]],
            },
        },
        version: {
            type: DataTypes.INTEGER,
            allowNull: true,
            validate: {
                isNumeric: true,
            },
        },
        cod_tipo_plan_carrera: {
            type: DataTypes.INTEGER,
            allowNull: true,
            validate: {
                isIn: [[1, 2, 3]],
            },
        },
        caracteristicas_tipo_plan: {
            type: DataTypes.STRING,
            allowNull: true,
            // validate: {
            //     is: /^[A-ZÀ-ÖØ-Þ0-9\s]+$/,
            // },
        },
        duracion_estudios: {
            type: DataTypes.INTEGER,
            allowNull: true,
            validate: {
                isNumeric: true,
            },
        },
        duracion_titulacion: {
            type: DataTypes.INTEGER,
            allowNull: true,
            validate: {
                isNumeric: true,
            },
        },
        duracion_total: {
            type: DataTypes.INTEGER,
            allowNull: true,
            validate: {
                isNumeric: true,
            },
        },
        regimen: {
            type: DataTypes.INTEGER,
            allowNull: true,
            validate: {
                isIn: [[1, 2, 3, 4]],
            },
        },
        duracion_regimen: {
            type: DataTypes.INTEGER,
            allowNull: true,
            validate: {
                isNumeric: true,
            },
        },
        nombre_titulo: {
            type: DataTypes.STRING,
            allowNull: true,
            // validate: {
            //     is: /^[A-ZÀ-ÖØ-Þ0-9\s]+$/,
            // },
        },
        nombre_grado: {
            type: DataTypes.STRING,
            allowNull: true,
            // validate: {
            //     is: /^[A-ZÀ-ÖØ-Þ0-9\s]+$/,
            // },
        },
        cod_nivel_global: {
            type: DataTypes.INTEGER,
            allowNull: true,
            validate: {
                isIn: [[1, 2, 3]],
            },
        },
        cod_nivel_carrera: {
            type: DataTypes.INTEGER,
            allowNull: true,
            validate: {
                isIn: [[0, 1, 2, 3, 4, 5, 6, 7, 8, 9]],
            },
        },
        cod_demre: {
            type: DataTypes.INTEGER,
            allowNull: true,
            validate: {
                isNumeric: true,
            },
        },
        anio_inicio: {
            type: DataTypes.INTEGER,
            allowNull: true,
            validate: {
                isNumeric: true,
            },
        },
        acreditacion: {
            type: DataTypes.INTEGER,
            allowNull: true,
            validate: {
                isIn: [[1, 2]],
            },
        },
        elegible_beca_pedagogia: {
            type: DataTypes.INTEGER,
            allowNull: true,
            validate: {
                isIn: [[0, 1, 2, 3]],
            },
        },
        ped_med_odont_otro: {
            type: DataTypes.STRING,
            allowNull: true,
            validate: {
                isIn: [['P', 'M', 'D', 'O']],
            },
        },
        requisito_ingreso: {
            type: DataTypes.INTEGER,
            allowNull: true,
            validate: {
                isIn: [[1, 2, 3, 4, 5, 6, 7, 8, 9, 10]],
            },
        },
        semestres_reconocidos: {
            type: DataTypes.INTEGER,
            allowNull: true,
            validate: {
                isNumeric: true,
            },
        },
        area_actual: {
            type: DataTypes.INTEGER,
            allowNull: true,
            validate: {
                isIn: [[1, 2, 3, 4, 5, 6, 7, 8]],
            },
        },
        area_destino_agricultura: {
            type: DataTypes.INTEGER,
            allowNull: true,
            validate: {
                isIn: [[0, 1]],
            },
        },
        area_destino_ciencias: {
            type: DataTypes.INTEGER,
            allowNull: true,
            validate: {
                isIn: [[0, 1]],
            },
        },
        area_destino_cs_sociales: {
            type: DataTypes.INTEGER,
            allowNull: true,
            validate: {
                isIn: [[0, 1]],
            },
        },
        area_destino_educacion: {
            type: DataTypes.INTEGER,
            allowNull: true,
            validate: {
                isIn: [[0, 1]],
            },
        },
        area_destino_humanidades: {
            type: DataTypes.INTEGER,
            allowNull: true,
            validate: {
                isIn: [[0, 1]],
            },
        },
        area_destino_ingenieria: {
            type: DataTypes.INTEGER,
            allowNull: true,
            validate: {
                isIn: [[0, 1]],
            },
        },
        area_destino_salud: {
            type: DataTypes.INTEGER,
            allowNull: true,
            validate: {
                isIn: [[0, 1]],
            },
        },
        area_destino_servicios: {
            type: DataTypes.INTEGER,
            allowNull: true,
            validate: {
                isIn: [[0, 1]],
            },
        },
        ponderacion_nem: {
            type: DataTypes.INTEGER,
            allowNull: true,
            validate: {
                min: 0,
                max: 100,
            },
        },
        ponderacion_ranking: {
            type: DataTypes.INTEGER,
            allowNull: true,
            validate: {
                min: 0,
                max: 100,
            }, 
        },
        ponderacion_c_lectora: {
            type: DataTypes.INTEGER,
            allowNull: true,
            validate: {
                min: 0,
                max: 100,
            },
        },
        ponderacion_matematicas: {
            type: DataTypes.INTEGER,
            allowNull: true,
            validate: {
                min: 0,
                max: 100,
            },
        },
        ponderacion_matematicas_2: {
            type: DataTypes.INTEGER,
            allowNull: true,
            validate: {
                min: 0,
                max: 100,
            },
        },
        ponderacion_historia: {
            type: DataTypes.INTEGER,
            allowNull: true,
            validate: {
                min: 0,
                max: 100,
            },
        },
        ponderacion_ciencias: {
            type: DataTypes.INTEGER,
            allowNull: true,
            validate: {
                min: 0,
                max: 100,
            },
        },
        ponderacion_otros: {
            type: DataTypes.INTEGER,
            allowNull: true,
            validate: {
                min: 0,
                max: 100,
            },
        },
        vacantes_primer_semestre: {
            type: DataTypes.INTEGER,
            allowNull: true,
            validate: {
                isNumeric: true,
            },
        },
        vacantes_segundo_semestre: {
            type: DataTypes.INTEGER,
            allowNull: true,
            validate: {
                isNumeric: true,
            },
        },
        vacantes_pace: {
            type: DataTypes.INTEGER,
            allowNull: true,
            validate: {
                isNumeric: true,
            },
        },
        malla_curricular: {
            type: DataTypes.STRING,
            allowNull: true,
            validate: {
                isUrl: true,
            },
        },
        perfil_egreso: {
            type: DataTypes.TEXT,
            allowNull: true,
            // validate: {
            //     is: /^[A-Za-zÀ-ÖØ-öø-ÿ0-9\s.,;:()[\]{}'"¡!¿?@#$%&*+-_=<>]+$/,
            // },
        },
        texto_requisito_ingreso: {
            type: DataTypes.TEXT,
            allowNull: true,
            // validate: {
            //     is: /^[A-Za-zÀ-ÖØ-öø-ÿ0-9\s.,;:()[\]{}'"¡!¿?@#$%&*+-_=<>]+$/,
            // },
        },
        otros_requisitos: {
            type: DataTypes.TEXT,
            allowNull: true,
            // validate: {
            //     is: /^[A-Za-zÀ-ÖØ-öø-ÿ0-9\s.,;:()[\]{}'"¡!¿?@#$%&*+-_=<>-]+$/,
            // },
        },
        mail_difusion_carrera: {
            type: DataTypes.TEXT,
            allowNull: true,
            validate: {
                isEmail: true,
            },
        },
        vigencia_carrera: {
            type: DataTypes.INTEGER,
            allowNull: true,
            validate: {
                isIn: [[1, 2, 3]],
            },
        }, 
        codigo_ies_num: {
            type: DataTypes.INTEGER,
            allowNull: true,
            validate: {
                isNumeric: true,
            },
        },
        cantidad_matricula_dfe: {
            type: DataTypes.INTEGER,
            allowNull: true,
            validate: {
                isNumeric: true,
            },
        },
        cantidad_beneficio_dfe: {
            type: DataTypes.INTEGER,
            allowNull: true,
            validate: {
                isNumeric: true,
            },
        },
    },
    {
        sequelize,
        modelName: "OAOfertaAcademica",
    schema: 'oferta_academica',
    tableName: "oa_ofertas_academicas",
    }
);

OAEtapa.hasMany(OAOfertaAcademica, { foreignKey: 'etapa_id', onDelete: 'RESTRICT' });
OAOfertaAcademica.belongsTo(OAEtapa, { foreignKey: 'etapa_id', onDelete: 'RESTRICT' });
export default OAOfertaAcademica;
